import { apiClient } from './apiClient';
import { API_ENDPOINTS } from '../constants';
import { ApiResponse, User, Address, PaymentMethod } from '../types';

class UserService {
  async getProfile(): Promise<ApiResponse<{ user: User }>> {
    const response = await apiClient.get<ApiResponse<{ user: User }>>(
      API_ENDPOINTS.USER.PROFILE
    );
    return response.data;
  }

  async updateProfile(profileData: Partial<User>): Promise<ApiResponse<User>> {
    const response = await apiClient.put<ApiResponse<User>>(
      API_ENDPOINTS.USER.UPDATE_PROFILE,
      profileData
    );
    return response.data;
  }

  async getAddresses(): Promise<ApiResponse<Address[]>> {
    const response = await apiClient.get<ApiResponse<Address[]>>(
      API_ENDPOINTS.USER.ADDRESSES
    );
    return response.data;
  }

  async addAddress(addressData: Omit<Address, 'id' | 'userId'>): Promise<ApiResponse<Address>> {
    const response = await apiClient.post<ApiResponse<Address>>(
      API_ENDPOINTS.USER.ADDRESSES,
      addressData
    );
    return response.data;
  }

  async updateAddress(addressId: string, addressData: Partial<Address>): Promise<ApiResponse<Address>> {
    const response = await apiClient.put<ApiResponse<Address>>(
      `${API_ENDPOINTS.USER.ADDRESSES}/${addressId}`,
      addressData
    );
    return response.data;
  }

  async deleteAddress(addressId: string): Promise<ApiResponse<boolean>> {
    const response = await apiClient.delete<ApiResponse<boolean>>(
      `${API_ENDPOINTS.USER.ADDRESSES}/${addressId}`
    );
    return response.data;
  }

  async getPaymentMethods(): Promise<ApiResponse<PaymentMethod[]>> {
    const response = await apiClient.get<ApiResponse<PaymentMethod[]>>(
      API_ENDPOINTS.USER.PAYMENT_METHODS
    );
    return response.data;
  }

  async addPaymentMethod(paymentData: Omit<PaymentMethod, 'id'>): Promise<ApiResponse<PaymentMethod>> {
    const response = await apiClient.post<ApiResponse<PaymentMethod>>(
      API_ENDPOINTS.USER.PAYMENT_METHODS,
      paymentData
    );
    return response.data;
  }

  async deletePaymentMethod(paymentMethodId: string): Promise<ApiResponse<boolean>> {
    const response = await apiClient.delete<ApiResponse<boolean>>(
      `${API_ENDPOINTS.USER.PAYMENT_METHODS}/${paymentMethodId}`
    );
    return response.data;
  }
}

export const userService = new UserService();
export default userService;
