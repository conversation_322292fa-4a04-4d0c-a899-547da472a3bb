import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { AuthStackParamList } from '../../types';
import { COLORS, SIZES, FONTS } from '../../constants';

type WelcomeScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'Welcome'>;

const WelcomeScreen: React.FC = () => {
  const navigation = useNavigation<WelcomeScreenNavigationProp>();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.PRIMARY} />
      <LinearGradient
        colors={[COLORS.PRIMARY, COLORS.SECONDARY]}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* Logo/Icon */}
          <View style={styles.logoContainer}>
            <View style={styles.logoPlaceholder}>
              <Text style={styles.logoText}>🍕</Text>
            </View>
            <Text style={styles.appName}>FoodWay</Text>
            <Text style={styles.tagline}>Delicious food delivered to your door</Text>
          </View>

          {/* Illustration */}
          <View style={styles.illustrationContainer}>
            <Text style={styles.illustration}>🚚</Text>
          </View>

          {/* Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.primaryButton}
              onPress={() => navigation.navigate('Register')}
            >
              <Text style={styles.primaryButtonText}>Get Started</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.secondaryButton}
              onPress={() => navigation.navigate('Login')}
            >
              <Text style={styles.secondaryButtonText}>I already have an account</Text>
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.PADDING_LARGE,
    paddingVertical: SIZES.PADDING_XLARGE,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: SIZES.MARGIN_XLARGE * 2,
  },
  logoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: COLORS.BACKGROUND,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SIZES.MARGIN_LARGE,
  },
  logoText: {
    fontSize: 40,
  },
  appName: {
    fontSize: SIZES.FONT_HEADER,
    fontWeight: 'bold',
    color: COLORS.BACKGROUND,
    marginBottom: SIZES.MARGIN_SMALL,
  },
  tagline: {
    fontSize: SIZES.FONT_LARGE,
    color: COLORS.BACKGROUND,
    textAlign: 'center',
    opacity: 0.9,
  },
  illustrationContainer: {
    alignItems: 'center',
    marginVertical: SIZES.MARGIN_XLARGE,
  },
  illustration: {
    fontSize: 80,
  },
  buttonContainer: {
    marginBottom: SIZES.MARGIN_XLARGE,
  },
  primaryButton: {
    backgroundColor: COLORS.BACKGROUND,
    paddingVertical: SIZES.PADDING_MEDIUM,
    paddingHorizontal: SIZES.PADDING_LARGE,
    borderRadius: SIZES.BORDER_RADIUS_LARGE,
    marginBottom: SIZES.MARGIN_MEDIUM,
  },
  primaryButtonText: {
    color: COLORS.PRIMARY,
    fontSize: SIZES.FONT_LARGE,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  secondaryButton: {
    paddingVertical: SIZES.PADDING_MEDIUM,
    paddingHorizontal: SIZES.PADDING_LARGE,
  },
  secondaryButtonText: {
    color: COLORS.BACKGROUND,
    fontSize: SIZES.FONT_MEDIUM,
    textAlign: 'center',
    textDecorationLine: 'underline',
  },
});

export default WelcomeScreen;
