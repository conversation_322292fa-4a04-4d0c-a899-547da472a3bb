import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  TextInputProps,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { COLORS, SIZES } from '../../constants';

interface CustomTextInputProps extends TextInputProps {
  label?: string;
  error?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
}

const CustomTextInput: React.FC<CustomTextInputProps> = ({
  label,
  error,
  leftIcon,
  rightIcon,
  onRightIconPress,
  secureTextEntry,
  style,
  ...props
}) => {
  const [isSecure, setIsSecure] = useState(secureTextEntry);
  const [isFocused, setIsFocused] = useState(false);

  const toggleSecureEntry = () => {
    setIsSecure(!isSecure);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    props.onBlur && props.onBlur({} as any);
  };

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <View style={[
        styles.inputContainer,
        isFocused && styles.inputContainerFocused,
        error && styles.inputContainerError,
      ]}>
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={SIZES.ICON_MEDIUM}
            color={isFocused ? COLORS.PRIMARY : COLORS.TEXT_SECONDARY}
            style={styles.leftIcon}
          />
        )}
        
        <TextInput
          {...props}
          style={[styles.input, leftIcon && styles.inputWithLeftIcon]}
          secureTextEntry={isSecure}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholderTextColor={COLORS.TEXT_LIGHT}
        />
        
        {secureTextEntry && (
          <TouchableOpacity onPress={toggleSecureEntry} style={styles.rightIcon}>
            <Ionicons
              name={isSecure ? 'eye-off-outline' : 'eye-outline'}
              size={SIZES.ICON_MEDIUM}
              color={COLORS.TEXT_SECONDARY}
            />
          </TouchableOpacity>
        )}
        
        {rightIcon && !secureTextEntry && (
          <TouchableOpacity onPress={onRightIconPress} style={styles.rightIcon}>
            <Ionicons
              name={rightIcon}
              size={SIZES.ICON_MEDIUM}
              color={COLORS.TEXT_SECONDARY}
            />
          </TouchableOpacity>
        )}
      </View>
      
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SIZES.MARGIN_MEDIUM,
  },
  label: {
    fontSize: SIZES.FONT_MEDIUM,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.MARGIN_SMALL,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: SIZES.BORDER_RADIUS_MEDIUM,
    backgroundColor: COLORS.BACKGROUND,
    paddingHorizontal: SIZES.PADDING_MEDIUM,
    minHeight: 50,
  },
  inputContainerFocused: {
    borderColor: COLORS.PRIMARY,
    borderWidth: 2,
  },
  inputContainerError: {
    borderColor: COLORS.ERROR,
  },
  input: {
    flex: 1,
    fontSize: SIZES.FONT_MEDIUM,
    color: COLORS.TEXT_PRIMARY,
    paddingVertical: SIZES.PADDING_SMALL,
  },
  inputWithLeftIcon: {
    marginLeft: SIZES.MARGIN_SMALL,
  },
  leftIcon: {
    marginRight: SIZES.MARGIN_SMALL,
  },
  rightIcon: {
    padding: SIZES.PADDING_SMALL,
  },
  errorText: {
    fontSize: SIZES.FONT_SMALL,
    color: COLORS.ERROR,
    marginTop: SIZES.MARGIN_SMALL,
  },
});

export default CustomTextInput;
