import { apiClient } from './apiClient';
import { API_ENDPOINTS } from '../constants';
import { LoginForm, RegisterForm, ApiResponse, AuthResponse, AuthTokens } from '../types';

class AuthService {
  async login(credentials: LoginForm): Promise<ApiResponse<AuthResponse>> {
    const response = await apiClient.post<ApiResponse<AuthResponse>>(
      API_ENDPOINTS.AUTH.LOGIN,
      credentials
    );
    return response.data;
  }

  async register(userData: Omit<RegisterForm, 'confirmPassword'>): Promise<ApiResponse<AuthResponse>> {
    console.log('Auth service - register called with:', userData);
    console.log('Register URL:', API_ENDPOINTS.BASE_URL + API_ENDPOINTS.AUTH.REGISTER);

    try {
      const response = await apiClient.post<ApiResponse<AuthResponse>>(
        API_ENDPOINTS.AUTH.REGISTER,
        userData
      );
      console.log('Auth service - register response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Auth service - register error:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      throw error;
    }
  }

  async logout(): Promise<ApiResponse<null>> {
    const response = await apiClient.post<ApiResponse<null>>(
      API_ENDPOINTS.AUTH.LOGOUT
    );
    return response.data;
  }

  async refreshToken(refreshToken: string): Promise<ApiResponse<AuthTokens>> {
    const response = await apiClient.post<ApiResponse<AuthTokens>>(
      API_ENDPOINTS.AUTH.REFRESH,
      { refreshToken }
    );
    return response.data;
  }

  async forgotPassword(email: string): Promise<ApiResponse<boolean>> {
    const response = await apiClient.post<ApiResponse<boolean>>(
      API_ENDPOINTS.AUTH.FORGOT_PASSWORD,
      { email }
    );
    return response.data;
  }

  async resetPassword(token: string, newPassword: string): Promise<ApiResponse<boolean>> {
    const response = await apiClient.post<ApiResponse<boolean>>(
      API_ENDPOINTS.AUTH.RESET_PASSWORD,
      { token, newPassword }
    );
    return response.data;
  }
}

export const authService = new AuthService();
export default authService;
