import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

import { COLORS, SIZES } from '../../constants';

interface CustomButtonProps {
  title: string;
  onPress: () => void;
  loading?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
  textStyle?: TextStyle;
  gradient?: boolean;
}

const CustomButton: React.FC<CustomButtonProps> = ({
  title,
  onPress,
  loading = false,
  disabled = false,
  variant = 'primary',
  size = 'medium',
  style,
  textStyle,
  gradient = false,
}) => {
  const isDisabled = disabled || loading;

  const getButtonStyle = () => {
    const baseStyle = [styles.button, styles[`${size}Button`]];
    
    switch (variant) {
      case 'primary':
        return [...baseStyle, styles.primaryButton, isDisabled && styles.disabledButton];
      case 'secondary':
        return [...baseStyle, styles.secondaryButton, isDisabled && styles.disabledButton];
      case 'outline':
        return [...baseStyle, styles.outlineButton, isDisabled && styles.disabledOutlineButton];
      case 'text':
        return [...baseStyle, styles.textButton];
      default:
        return [...baseStyle, styles.primaryButton, isDisabled && styles.disabledButton];
    }
  };

  const getTextStyle = () => {
    const baseStyle = [styles.buttonText, styles[`${size}Text`]];
    
    switch (variant) {
      case 'primary':
        return [...baseStyle, styles.primaryText, isDisabled && styles.disabledText];
      case 'secondary':
        return [...baseStyle, styles.secondaryText, isDisabled && styles.disabledText];
      case 'outline':
        return [...baseStyle, styles.outlineText, isDisabled && styles.disabledOutlineText];
      case 'text':
        return [...baseStyle, styles.textButtonText];
      default:
        return [...baseStyle, styles.primaryText, isDisabled && styles.disabledText];
    }
  };

  const renderButton = () => (
    <TouchableOpacity
      style={[...getButtonStyle(), style]}
      onPress={onPress}
      disabled={isDisabled}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={variant === 'outline' || variant === 'text' ? COLORS.PRIMARY : COLORS.BACKGROUND}
        />
      ) : (
        <Text style={[...getTextStyle(), textStyle]}>{title}</Text>
      )}
    </TouchableOpacity>
  );

  if (gradient && variant === 'primary' && !isDisabled) {
    return (
      <TouchableOpacity
        style={[styles.button, styles[`${size}Button`], style]}
        onPress={onPress}
        disabled={isDisabled}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={[COLORS.PRIMARY, COLORS.SECONDARY]}
          style={styles.gradientButton}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          {loading ? (
            <ActivityIndicator size="small" color={COLORS.BACKGROUND} />
          ) : (
            <Text style={[...getTextStyle(), textStyle]}>{title}</Text>
          )}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return renderButton();
};

const styles = StyleSheet.create({
  button: {
    borderRadius: SIZES.BORDER_RADIUS_MEDIUM,
    alignItems: 'center',
    justifyContent: 'center',
  },
  gradientButton: {
    flex: 1,
    borderRadius: SIZES.BORDER_RADIUS_MEDIUM,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // Size variants
  smallButton: {
    paddingVertical: SIZES.PADDING_SMALL,
    paddingHorizontal: SIZES.PADDING_MEDIUM,
    minHeight: 36,
  },
  mediumButton: {
    paddingVertical: SIZES.PADDING_MEDIUM,
    paddingHorizontal: SIZES.PADDING_LARGE,
    minHeight: 48,
  },
  largeButton: {
    paddingVertical: SIZES.PADDING_LARGE,
    paddingHorizontal: SIZES.PADDING_XLARGE,
    minHeight: 56,
  },
  
  // Button variants
  primaryButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  secondaryButton: {
    backgroundColor: COLORS.SECONDARY,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: COLORS.PRIMARY,
  },
  textButton: {
    backgroundColor: 'transparent',
  },
  
  // Disabled states
  disabledButton: {
    backgroundColor: COLORS.TEXT_LIGHT,
  },
  disabledOutlineButton: {
    borderColor: COLORS.TEXT_LIGHT,
  },
  
  // Text styles
  buttonText: {
    fontWeight: '600',
    textAlign: 'center',
  },
  smallText: {
    fontSize: SIZES.FONT_SMALL,
  },
  mediumText: {
    fontSize: SIZES.FONT_MEDIUM,
  },
  largeText: {
    fontSize: SIZES.FONT_LARGE,
  },
  
  // Text color variants
  primaryText: {
    color: COLORS.BACKGROUND,
  },
  secondaryText: {
    color: COLORS.BACKGROUND,
  },
  outlineText: {
    color: COLORS.PRIMARY,
  },
  textButtonText: {
    color: COLORS.PRIMARY,
  },
  
  // Disabled text
  disabledText: {
    color: COLORS.TEXT_SECONDARY,
  },
  disabledOutlineText: {
    color: COLORS.TEXT_LIGHT,
  },
});

export default CustomButton;
