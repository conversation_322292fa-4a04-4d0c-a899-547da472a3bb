{"name": "foodway_customer__app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/slider": "^4.5.7", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.5.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "cors": "^2.8.5", "expo": "~53.0.15", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-location": "^18.1.6", "expo-notifications": "^0.31.3", "expo-permissions": "^14.4.0", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "react": "19.0.0", "react-hook-form": "^7.59.0", "react-native": "0.79.4", "react-native-async-storage": "^0.0.1", "react-native-elements": "^3.4.3", "react-native-maps": "^1.24.3", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "yup": "^1.6.1", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}