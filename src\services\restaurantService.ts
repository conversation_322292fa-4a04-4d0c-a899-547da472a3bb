import { apiClient } from './apiClient';
import { API_ENDPOINTS } from '../constants';
import { ApiResponse, Restaurant, MenuItem, PaginatedResponse } from '../types';

class RestaurantService {
  async getRestaurants(page = 1, limit = 20): Promise<ApiResponse<PaginatedResponse<Restaurant>>> {
    const response = await apiClient.get<ApiResponse<PaginatedResponse<Restaurant>>>(
      `${API_ENDPOINTS.RESTAURANTS.LIST}?page=${page}&limit=${limit}`
    );
    return response.data;
  }

  async getRestaurantById(restaurantId: string): Promise<ApiResponse<Restaurant>> {
    const url = API_ENDPOINTS.RESTAURANTS.DETAIL.replace(':id', restaurantId);
    const response = await apiClient.get<ApiResponse<Restaurant>>(url);
    return response.data;
  }

  async getRestaurantMenu(restaurantId: string): Promise<ApiResponse<MenuItem[]>> {
    const url = API_ENDPOINTS.RESTAURANTS.MENU.replace(':id', restaurantId);
    const response = await apiClient.get<ApiResponse<MenuItem[]>>(url);
    return response.data;
  }

  async searchRestaurants(query: string, filters?: {
    cuisine?: string[];
    priceRange?: [number, number];
    rating?: number;
    deliveryTime?: number;
  }): Promise<ApiResponse<Restaurant[]>> {
    const params = new URLSearchParams({
      q: query,
      ...(filters?.cuisine && { cuisine: filters.cuisine.join(',') }),
      ...(filters?.priceRange && { 
        minPrice: filters.priceRange[0].toString(),
        maxPrice: filters.priceRange[1].toString()
      }),
      ...(filters?.rating && { rating: filters.rating.toString() }),
      ...(filters?.deliveryTime && { deliveryTime: filters.deliveryTime.toString() }),
    });

    const response = await apiClient.get<ApiResponse<Restaurant[]>>(
      `${API_ENDPOINTS.RESTAURANTS.SEARCH}?${params.toString()}`
    );
    return response.data;
  }

  async getNearbyRestaurants(
    latitude: number, 
    longitude: number, 
    radius = 10000
  ): Promise<ApiResponse<Restaurant[]>> {
    const response = await apiClient.get<ApiResponse<Restaurant[]>>(
      `${API_ENDPOINTS.RESTAURANTS.NEARBY}?lat=${latitude}&lng=${longitude}&radius=${radius}`
    );
    return response.data;
  }

  async getRestaurantsByCategory(category: string): Promise<ApiResponse<Restaurant[]>> {
    const response = await apiClient.get<ApiResponse<Restaurant[]>>(
      `${API_ENDPOINTS.RESTAURANTS.LIST}?category=${category}`
    );
    return response.data;
  }

  async getFeaturedRestaurants(): Promise<ApiResponse<Restaurant[]>> {
    const response = await apiClient.get<ApiResponse<Restaurant[]>>(
      `${API_ENDPOINTS.RESTAURANTS.LIST}?featured=true`
    );
    return response.data;
  }
}

export const restaurantService = new RestaurantService();
export default restaurantService;
