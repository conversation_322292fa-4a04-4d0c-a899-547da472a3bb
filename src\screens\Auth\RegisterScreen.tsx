import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';

import { AuthStackParamList, RegisterForm } from '../../types';
import { COLORS, SIZES, VALIDATION_RULES } from '../../constants';
import { registerUser } from '../../store/slices/authSlice';
import { RootState, AppDispatch } from '../../store';
import CustomTextInput from '../../components/forms/CustomTextInput';
import CustomButton from '../../components/common/CustomButton';
import ApiTestButton from '../../components/common/ApiTestButton';

type RegisterScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'Register'>;

const schema = yup.object().shape({
  firstName: yup
    .string()
    .required('First name is required')
    .min(VALIDATION_RULES.NAME_MIN_LENGTH, `First name must be at least ${VALIDATION_RULES.NAME_MIN_LENGTH} characters`)
    .max(VALIDATION_RULES.NAME_MAX_LENGTH, `First name must be less than ${VALIDATION_RULES.NAME_MAX_LENGTH} characters`),
  lastName: yup
    .string()
    .required('Last name is required')
    .min(VALIDATION_RULES.NAME_MIN_LENGTH, `Last name must be at least ${VALIDATION_RULES.NAME_MIN_LENGTH} characters`)
    .max(VALIDATION_RULES.NAME_MAX_LENGTH, `Last name must be less than ${VALIDATION_RULES.NAME_MAX_LENGTH} characters`),
  email: yup
    .string()
    .required('Email is required')
    .matches(VALIDATION_RULES.EMAIL_REGEX, 'Please enter a valid email'),
  phone: yup
    .string()
    .required('Phone number is required')
    .matches(VALIDATION_RULES.PHONE_REGEX, 'Please enter a valid phone number'),
  password: yup
    .string()
    .required('Password is required')
    .min(VALIDATION_RULES.PASSWORD_MIN_LENGTH, `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters`)
    .matches(VALIDATION_RULES.PASSWORD_REGEX, 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  confirmPassword: yup
    .string()
    .required('Please confirm your password')
    .oneOf([yup.ref('password')], 'Passwords must match'),
});

const RegisterScreen: React.FC = () => {
  const navigation = useNavigation<RegisterScreenNavigationProp>();
  const dispatch = useDispatch<AppDispatch>();
  const { loading, error } = useSelector((state: RootState) => state.auth);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterForm>({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    },
  });

  const testApiConnection = async () => {
    try {
      console.log('Testing API connection...');
      const response = await fetch('https://backend-production-f106.up.railway.app/api/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: 'Test',
          lastName: 'User',
          email: `test${Date.now()}@example.com`,
          password: 'TestPassword123!',
          phone: '+1234567890',
        }),
      });

      console.log('Direct fetch response status:', response.status);
      const responseText = await response.text();
      console.log('Direct fetch response:', responseText);
    } catch (error) {
      console.error('Direct fetch error:', error);
    }
  };

  const onSubmit = async (data: RegisterForm) => {
    try {
      console.log('Registration data:', data);

      const result = await dispatch(registerUser(data)).unwrap();
      console.log('Registration successful:', result);
      Alert.alert('Success', 'Registration successful! Please check your email to verify your account.');
      // Navigation will be handled by AppNavigator based on auth state
    } catch (error: any) {
      console.error('Registration error:', error);
      console.error('Full error object:', JSON.stringify(error, null, 2));

      let errorMessage = 'Registration failed';
      if (error?.response?.data?.error?.message) {
        errorMessage = error.response.data.error.message;

        // If there are validation details, show them
        if (error.response.data.error.details && Array.isArray(error.response.data.error.details)) {
          const validationErrors = error.response.data.error.details
            .map((detail: any) => `${detail.field}: ${detail.message}`)
            .join('\n');
          errorMessage += '\n\nDetails:\n' + validationErrors;
        }
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      Alert.alert('Registration Failed', errorMessage);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Create Account</Text>
          <Text style={styles.subtitle}>Join us and start ordering!</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.nameRow}>
            <Controller
              control={control}
              name="firstName"
              render={({ field: { onChange, onBlur, value } }) => (
                <CustomTextInput
                  label="First Name"
                  placeholder="First name"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.firstName?.message}
                  style={styles.halfInput}
                  autoCapitalize="words"
                />
              )}
            />

            <Controller
              control={control}
              name="lastName"
              render={({ field: { onChange, onBlur, value } }) => (
                <CustomTextInput
                  label="Last Name"
                  placeholder="Last name"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.lastName?.message}
                  style={styles.halfInput}
                  autoCapitalize="words"
                />
              )}
            />
          </View>

          <Controller
            control={control}
            name="email"
            render={({ field: { onChange, onBlur, value } }) => (
              <CustomTextInput
                label="Email"
                placeholder="Enter your email"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                error={errors.email?.message}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
              />
            )}
          />

          <Controller
            control={control}
            name="phone"
            render={({ field: { onChange, onBlur, value } }) => (
              <CustomTextInput
                label="Phone Number"
                placeholder="+1234567890 (include country code)"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                error={errors.phone?.message}
                keyboardType="phone-pad"
                autoComplete="tel"
              />
            )}
          />

          <Controller
            control={control}
            name="password"
            render={({ field: { onChange, onBlur, value } }) => (
              <CustomTextInput
                label="Password"
                placeholder="Min 8 chars, include A-Z, a-z, 0-9, @$!%*?&"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                error={errors.password?.message}
                secureTextEntry
                autoComplete="password-new"
              />
            )}
          />

          <Controller
            control={control}
            name="confirmPassword"
            render={({ field: { onChange, onBlur, value } }) => (
              <CustomTextInput
                label="Confirm Password"
                placeholder="Confirm your password"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                error={errors.confirmPassword?.message}
                secureTextEntry
                autoComplete="password-new"
              />
            )}
          />

          <ApiTestButton />

          <CustomButton
            title="Create Account"
            onPress={handleSubmit(onSubmit)}
            loading={loading}
            style={styles.registerButton}
            gradient
          />

          {error && (
            <Text style={styles.errorText}>{error}</Text>
          )}
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Already have an account? </Text>
          <TouchableOpacity onPress={() => navigation.navigate('Login')}>
            <Text style={styles.signInText}>Sign In</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: SIZES.PADDING_LARGE,
    paddingVertical: SIZES.PADDING_MEDIUM,
  },
  header: {
    alignItems: 'center',
    marginBottom: SIZES.MARGIN_LARGE,
  },
  title: {
    fontSize: SIZES.FONT_TITLE,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.MARGIN_SMALL,
  },
  subtitle: {
    fontSize: SIZES.FONT_MEDIUM,
    color: COLORS.TEXT_SECONDARY,
  },
  form: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfInput: {
    flex: 0.48,
  },
  testButton: {
    marginTop: SIZES.MARGIN_MEDIUM,
    marginBottom: SIZES.MARGIN_SMALL,
  },
  registerButton: {
    marginBottom: SIZES.MARGIN_MEDIUM,
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: SIZES.FONT_SMALL,
    textAlign: 'center',
    marginTop: SIZES.MARGIN_SMALL,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SIZES.MARGIN_MEDIUM,
  },
  footerText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: SIZES.FONT_MEDIUM,
  },
  signInText: {
    color: COLORS.PRIMARY,
    fontSize: SIZES.FONT_MEDIUM,
    fontWeight: 'bold',
  },
});

export default RegisterScreen;
