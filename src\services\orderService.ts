import { apiClient } from './apiClient';
import { API_ENDPOINTS } from '../constants';
import { ApiResponse, Order, PaginatedResponse } from '../types';

class OrderService {
  async createOrder(orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Order>> {
    const response = await apiClient.post<ApiResponse<Order>>(
      API_ENDPOINTS.ORDERS.CREATE,
      orderData
    );
    return response.data;
  }

  async getOrders(page = 1, limit = 20): Promise<ApiResponse<PaginatedResponse<Order>>> {
    const response = await apiClient.get<ApiResponse<PaginatedResponse<Order>>>(
      `${API_ENDPOINTS.ORDERS.LIST}?page=${page}&limit=${limit}`
    );
    return response.data;
  }

  async getOrderById(orderId: string): Promise<ApiResponse<Order>> {
    const url = API_ENDPOINTS.ORDERS.DETAIL.replace(':id', orderId);
    const response = await apiClient.get<ApiResponse<Order>>(url);
    return response.data;
  }

  async trackOrder(orderId: string): Promise<ApiResponse<Order>> {
    const url = API_ENDPOINTS.ORDERS.TRACK.replace(':id', orderId);
    const response = await apiClient.get<ApiResponse<Order>>(url);
    return response.data;
  }

  async cancelOrder(orderId: string): Promise<ApiResponse<boolean>> {
    const url = API_ENDPOINTS.ORDERS.CANCEL.replace(':id', orderId);
    const response = await apiClient.post<ApiResponse<boolean>>(url);
    return response.data;
  }

  async getActiveOrders(): Promise<ApiResponse<Order[]>> {
    const response = await apiClient.get<ApiResponse<Order[]>>(
      `${API_ENDPOINTS.ORDERS.LIST}?status=pending,confirmed,preparing,ready,picked_up,on_the_way`
    );
    return response.data;
  }

  async getOrderHistory(): Promise<ApiResponse<Order[]>> {
    const response = await apiClient.get<ApiResponse<Order[]>>(
      `${API_ENDPOINTS.ORDERS.LIST}?status=delivered,cancelled`
    );
    return response.data;
  }
}

export const orderService = new OrderService();
export default orderService;
