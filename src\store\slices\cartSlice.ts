import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Cart, CartItem, MenuItem, SelectedCustomization } from '../../types';

interface CartState {
  cart: Cart | null;
  loading: boolean;
  error: string | null;
}

const initialState: CartState = {
  cart: null,
  loading: false,
  error: null,
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    initializeCart: (state, action: PayloadAction<{ restaurantId: string; restaurant: any }>) => {
      if (!state.cart || state.cart.restaurantId !== action.payload.restaurantId) {
        state.cart = {
          id: `cart_${Date.now()}`,
          restaurantId: action.payload.restaurantId,
          restaurant: action.payload.restaurant,
          items: [],
          subtotal: 0,
          deliveryFee: action.payload.restaurant.deliveryFee || 0,
          tax: 0,
          total: 0,
          estimatedDeliveryTime: action.payload.restaurant.deliveryTime || '30-45 min',
        };
      }
    },
    
    addToCart: (state, action: PayloadAction<{
      menuItem: MenuItem;
      quantity: number;
      customizations: SelectedCustomization[];
      specialInstructions?: string;
    }>) => {
      if (!state.cart) return;

      const { menuItem, quantity, customizations, specialInstructions } = action.payload;
      
      // Calculate customization price
      const customizationPrice = customizations.reduce((sum, custom) => sum + custom.price, 0);
      const itemTotalPrice = (menuItem.price + customizationPrice) * quantity;

      const cartItem: CartItem = {
        id: `item_${Date.now()}_${Math.random()}`,
        menuItem,
        quantity,
        customizations,
        specialInstructions,
        totalPrice: itemTotalPrice,
      };

      state.cart.items.push(cartItem);
      cartSlice.caseReducers.calculateTotals(state);
    },

    updateCartItem: (state, action: PayloadAction<{
      itemId: string;
      quantity: number;
      customizations?: SelectedCustomization[];
      specialInstructions?: string;
    }>) => {
      if (!state.cart) return;

      const { itemId, quantity, customizations, specialInstructions } = action.payload;
      const itemIndex = state.cart.items.findIndex(item => item.id === itemId);

      if (itemIndex !== -1) {
        const item = state.cart.items[itemIndex];
        item.quantity = quantity;
        
        if (customizations) {
          item.customizations = customizations;
        }
        
        if (specialInstructions !== undefined) {
          item.specialInstructions = specialInstructions;
        }

        // Recalculate item total price
        const customizationPrice = item.customizations.reduce((sum, custom) => sum + custom.price, 0);
        item.totalPrice = (item.menuItem.price + customizationPrice) * item.quantity;

        cartSlice.caseReducers.calculateTotals(state);
      }
    },

    removeFromCart: (state, action: PayloadAction<string>) => {
      if (!state.cart) return;

      state.cart.items = state.cart.items.filter(item => item.id !== action.payload);
      cartSlice.caseReducers.calculateTotals(state);
    },

    clearCart: (state) => {
      state.cart = null;
      state.error = null;
    },

    calculateTotals: (state) => {
      if (!state.cart) return;

      const subtotal = state.cart.items.reduce((sum, item) => sum + item.totalPrice, 0);
      const tax = subtotal * 0.08; // 8% tax rate
      const total = subtotal + state.cart.deliveryFee + tax;

      state.cart.subtotal = subtotal;
      state.cart.tax = tax;
      state.cart.total = total;
    },

    setCartError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },

    clearCartError: (state) => {
      state.error = null;
    },

    setCartLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
  },
});

export const {
  initializeCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  calculateTotals,
  setCartError,
  clearCartError,
  setCartLoading,
} = cartSlice.actions;

export default cartSlice.reducer;
