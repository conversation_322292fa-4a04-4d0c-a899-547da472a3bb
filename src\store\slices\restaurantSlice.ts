import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Restaurant, MenuItem } from '../../types';
import { restaurantService } from '../../services/restaurantService';

interface RestaurantState {
  restaurants: Restaurant[];
  currentRestaurant: Restaurant | null;
  menu: MenuItem[];
  loading: boolean;
  error: string | null;
  searchResults: Restaurant[];
  nearbyRestaurants: Restaurant[];
}

const initialState: RestaurantState = {
  restaurants: [],
  currentRestaurant: null,
  menu: [],
  loading: false,
  error: null,
  searchResults: [],
  nearbyRestaurants: [],
};

// Async thunks
export const fetchRestaurants = createAsyncThunk(
  'restaurant/fetchRestaurants',
  async (_, { rejectWithValue }) => {
    try {
      const response = await restaurantService.getRestaurants();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch restaurants');
    }
  }
);

export const fetchRestaurantById = createAsyncThunk(
  'restaurant/fetchById',
  async (restaurantId: string, { rejectWithValue }) => {
    try {
      const response = await restaurantService.getRestaurantById(restaurantId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch restaurant');
    }
  }
);

export const fetchRestaurantMenu = createAsyncThunk(
  'restaurant/fetchMenu',
  async (restaurantId: string, { rejectWithValue }) => {
    try {
      const response = await restaurantService.getRestaurantMenu(restaurantId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch menu');
    }
  }
);

export const searchRestaurants = createAsyncThunk(
  'restaurant/search',
  async (query: string, { rejectWithValue }) => {
    try {
      const response = await restaurantService.searchRestaurants(query);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Search failed');
    }
  }
);

const restaurantSlice = createSlice({
  name: 'restaurant',
  initialState,
  reducers: {
    clearRestaurantError: (state) => {
      state.error = null;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
    setCurrentRestaurant: (state, action: PayloadAction<Restaurant>) => {
      state.currentRestaurant = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Restaurants
      .addCase(fetchRestaurants.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRestaurants.fulfilled, (state, action) => {
        state.loading = false;
        state.restaurants = action.payload;
      })
      .addCase(fetchRestaurants.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Fetch Restaurant by ID
      .addCase(fetchRestaurantById.fulfilled, (state, action) => {
        state.currentRestaurant = action.payload;
      })
      // Fetch Menu
      .addCase(fetchRestaurantMenu.fulfilled, (state, action) => {
        state.menu = action.payload;
      })
      // Search Restaurants
      .addCase(searchRestaurants.fulfilled, (state, action) => {
        state.searchResults = action.payload;
      });
  },
});

export const { clearRestaurantError, clearSearchResults, setCurrentRestaurant } = restaurantSlice.actions;
export default restaurantSlice.reducer;
