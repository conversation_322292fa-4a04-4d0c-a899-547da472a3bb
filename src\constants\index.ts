// App Configuration
export const APP_CONFIG = {
  APP_NAME: 'FoodWay',
  VERSION: '1.0.0',
  API_TIMEOUT: 10000,
  PAGINATION_LIMIT: 20,
};

// API Endpoints
export const API_ENDPOINTS = {
  BASE_URL: 'https://backend-production-f106.up.railway.app/api/v1',
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
  },
  USER: {
    PROFILE: '/user/profile',
    UPDATE_PROFILE: '/user/profile',
    ADDRESSES: '/user/addresses',
    PAYMENT_METHODS: '/user/payment-methods',
  },
  RESTAURANTS: {
    LIST: '/restaurants',
    DETAIL: '/restaurants/:id',
    MENU: '/restaurants/:id/menu',
    SEARCH: '/restaurants/search',
    NEARBY: '/restaurants/nearby',
  },
  ORDERS: {
    CREATE: '/orders',
    LIST: '/orders',
    DETAIL: '/orders/:id',
    TRACK: '/orders/:id/track',
    CANCEL: '/orders/:id/cancel',
  },
  CART: {
    GET: '/cart',
    ADD_ITEM: '/cart/items',
    UPDATE_ITEM: '/cart/items/:id',
    REMOVE_ITEM: '/cart/items/:id',
    CLEAR: '/cart/clear',
  },
};

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  CART_DATA: 'cart_data',
  RECENT_SEARCHES: 'recent_searches',
  FAVORITE_RESTAURANTS: 'favorite_restaurants',
  APP_SETTINGS: 'app_settings',
};

// Colors
export const COLORS = {
  PRIMARY: '#FF6B35',
  SECONDARY: '#F7931E',
  ACCENT: '#FFD23F',
  BACKGROUND: '#FFFFFF',
  SURFACE: '#F8F9FA',
  TEXT_PRIMARY: '#212529',
  TEXT_SECONDARY: '#6C757D',
  TEXT_LIGHT: '#ADB5BD',
  SUCCESS: '#28A745',
  WARNING: '#FFC107',
  ERROR: '#DC3545',
  INFO: '#17A2B8',
  BORDER: '#DEE2E6',
  SHADOW: 'rgba(0, 0, 0, 0.1)',
};

// Fonts
export const FONTS = {
  REGULAR: 'System',
  MEDIUM: 'System',
  BOLD: 'System',
  LIGHT: 'System',
};

// Sizes
export const SIZES = {
  // Font sizes
  FONT_SMALL: 12,
  FONT_MEDIUM: 14,
  FONT_LARGE: 16,
  FONT_XLARGE: 18,
  FONT_XXLARGE: 20,
  FONT_TITLE: 24,
  FONT_HEADER: 28,

  // Spacing
  PADDING_SMALL: 8,
  PADDING_MEDIUM: 16,
  PADDING_LARGE: 24,
  PADDING_XLARGE: 32,

  MARGIN_SMALL: 8,
  MARGIN_MEDIUM: 16,
  MARGIN_LARGE: 24,
  MARGIN_XLARGE: 32,

  // Border radius
  BORDER_RADIUS_SMALL: 4,
  BORDER_RADIUS_MEDIUM: 8,
  BORDER_RADIUS_LARGE: 12,
  BORDER_RADIUS_XLARGE: 16,

  // Icon sizes
  ICON_SMALL: 16,
  ICON_MEDIUM: 24,
  ICON_LARGE: 32,
  ICON_XLARGE: 40,
};

// Order Status Colors
export const ORDER_STATUS_COLORS = {
  pending: COLORS.WARNING,
  confirmed: COLORS.INFO,
  preparing: COLORS.SECONDARY,
  ready: COLORS.PRIMARY,
  picked_up: COLORS.PRIMARY,
  on_the_way: COLORS.PRIMARY,
  delivered: COLORS.SUCCESS,
  cancelled: COLORS.ERROR,
};

// Cuisine Types
export const CUISINE_TYPES = [
  'Italian',
  'Chinese',
  'Indian',
  'Mexican',
  'Thai',
  'Japanese',
  'American',
  'Mediterranean',
  'French',
  'Korean',
  'Vietnamese',
  'Greek',
  'Turkish',
  'Lebanese',
  'Spanish',
  'German',
  'British',
  'Brazilian',
  'Moroccan',
  'Ethiopian',
];

// Delivery Time Options
export const DELIVERY_TIME_OPTIONS = [
  { label: 'ASAP', value: 'asap' },
  { label: '15 minutes', value: '15' },
  { label: '30 minutes', value: '30' },
  { label: '45 minutes', value: '45' },
  { label: '1 hour', value: '60' },
  { label: '1.5 hours', value: '90' },
  { label: '2 hours', value: '120' },
];

// Payment Method Types
export const PAYMENT_METHODS = {
  CREDIT_CARD: 'credit_card',
  DEBIT_CARD: 'debit_card',
  PAYPAL: 'paypal',
  APPLE_PAY: 'apple_pay',
  GOOGLE_PAY: 'google_pay',
  CASH: 'cash',
};

// Map Configuration
export const MAP_CONFIG = {
  DEFAULT_LATITUDE: 37.7749,
  DEFAULT_LONGITUDE: -122.4194,
  DEFAULT_ZOOM: 15,
  SEARCH_RADIUS: 10000, // 10km in meters
};

// Validation Rules
export const VALIDATION_RULES = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^\+?[1-9]\d{1,14}$/, // International phone format
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_REGEX: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&/])[A-Za-z\d@$!%*?&/]+$/, // At least one lowercase, uppercase, digit, and special char
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'Session expired. Please login again.',
  FORBIDDEN: 'You do not have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Welcome back!',
  REGISTER_SUCCESS: 'Account created successfully!',
  ORDER_PLACED: 'Your order has been placed successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  ADDRESS_ADDED: 'Address added successfully!',
  PAYMENT_METHOD_ADDED: 'Payment method added successfully!',
};

// Animation Durations
export const ANIMATION_DURATION = {
  SHORT: 200,
  MEDIUM: 300,
  LONG: 500,
};

// Screen Names
export const SCREEN_NAMES = {
  // Auth Stack
  WELCOME: 'Welcome',
  LOGIN: 'Login',
  REGISTER: 'Register',
  FORGOT_PASSWORD: 'ForgotPassword',

  // Main Stack
  HOME: 'Home',
  SEARCH: 'Search',
  ORDERS: 'Orders',
  PROFILE: 'Profile',
  RESTAURANT_DETAIL: 'RestaurantDetail',
  MENU_ITEM_DETAIL: 'MenuItemDetail',
  CART: 'Cart',
  CHECKOUT: 'Checkout',
  ORDER_TRACKING: 'OrderTracking',
  EDIT_PROFILE: 'EditProfile',
  ADDRESSES: 'Addresses',
  ADD_ADDRESS: 'AddAddress',
  PAYMENT_METHODS: 'PaymentMethods',
  ORDER_HISTORY: 'OrderHistory',
  ORDER_DETAIL: 'OrderDetail',
};
