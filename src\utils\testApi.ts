// Simple API test utility
import { authService } from '../services/authService';
import { userService } from '../services/userService';

export const testApiConnection = async () => {
  try {
    console.log('Testing API connection to:', 'https://backend-production-f106.up.railway.app/api/v1');
    
    // Test a simple endpoint that doesn't require auth
    const response = await fetch('https://backend-production-f106.up.railway.app/api/v1/health');
    
    if (response.ok) {
      console.log('✅ API connection successful');
      return true;
    } else {
      console.log('❌ API connection failed:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ API connection error:', error);
    return false;
  }
};

export const testRegisterUser = async () => {
  try {
    const testUser = {
      firstName: 'Test',
      lastName: 'User',
      email: `test${Date.now()}@example.com`,
      password: 'TestPassword123!',
      phone: '+**********',
    };

    console.log('Testing user registration...');
    const response = await authService.register(testUser);
    console.log('✅ Registration successful:', response);
    return response;
  } catch (error: any) {
    console.log('❌ Registration failed:', error.response?.data || error.message);
    return null;
  }
};

export const testLoginUser = async (email: string, password: string) => {
  try {
    console.log('Testing user login...');
    const response = await authService.login({ email, password });
    console.log('✅ Login successful:', response);
    return response;
  } catch (error: any) {
    console.log('❌ Login failed:', error.response?.data || error.message);
    return null;
  }
};
