import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { RootStackParamList } from '../../types';
import { COLORS, SIZES } from '../../constants';
import { RootState, AppDispatch } from '../../store';
import { fetchRestaurants } from '../../store/slices/restaurantSlice';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Main'>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { restaurants, loading } = useSelector((state: RootState) => state.restaurant);

  useEffect(() => {
    dispatch(fetchRestaurants());
  }, [dispatch]);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Hello, {user?.firstName || 'User'}!</Text>
            <Text style={styles.subGreeting}>What would you like to eat today?</Text>
          </View>
          <TouchableOpacity style={styles.cartButton}>
            <Text style={styles.cartIcon}>🛒</Text>
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <TouchableOpacity 
          style={styles.searchBar}
          onPress={() => navigation.navigate('Search' as any)}
        >
          <Text style={styles.searchIcon}>🔍</Text>
          <Text style={styles.searchPlaceholder}>Search for restaurants or food</Text>
        </TouchableOpacity>

        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
            {['🍕 Pizza', '🍔 Burgers', '🍜 Asian', '🥗 Healthy', '🍰 Desserts', '☕ Coffee'].map((category, index) => (
              <TouchableOpacity key={index} style={styles.categoryCard}>
                <Text style={styles.categoryText}>{category}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Featured Restaurants */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Restaurants</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          {loading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Loading restaurants...</Text>
            </View>
          ) : (
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {[1, 2, 3, 4, 5].map((item) => (
                <TouchableOpacity 
                  key={item} 
                  style={styles.restaurantCard}
                  onPress={() => navigation.navigate('RestaurantDetail', { restaurantId: item.toString() })}
                >
                  <View style={styles.restaurantImage}>
                    <Text style={styles.restaurantImagePlaceholder}>🏪</Text>
                  </View>
                  <View style={styles.restaurantInfo}>
                    <Text style={styles.restaurantName}>Restaurant {item}</Text>
                    <Text style={styles.restaurantDetails}>⭐ 4.5 • 25-30 min • $2.99 delivery</Text>
                    <Text style={styles.restaurantCuisine}>Italian, Pizza</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </View>

        {/* Popular Near You */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popular Near You</Text>
          {[1, 2, 3].map((item) => (
            <TouchableOpacity key={item} style={styles.popularRestaurantCard}>
              <View style={styles.popularRestaurantImage}>
                <Text style={styles.restaurantImagePlaceholder}>🏪</Text>
              </View>
              <View style={styles.popularRestaurantInfo}>
                <Text style={styles.restaurantName}>Popular Restaurant {item}</Text>
                <Text style={styles.restaurantDetails}>⭐ 4.8 • 20-25 min</Text>
                <Text style={styles.restaurantCuisine}>American, Fast Food</Text>
              </View>
              <View style={styles.deliveryBadge}>
                <Text style={styles.deliveryBadgeText}>Free Delivery</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SIZES.PADDING_LARGE,
    paddingVertical: SIZES.PADDING_MEDIUM,
  },
  greeting: {
    fontSize: SIZES.FONT_TITLE,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  subGreeting: {
    fontSize: SIZES.FONT_MEDIUM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: 4,
  },
  cartButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.SURFACE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartIcon: {
    fontSize: 20,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.SURFACE,
    marginHorizontal: SIZES.PADDING_LARGE,
    marginBottom: SIZES.MARGIN_LARGE,
    paddingHorizontal: SIZES.PADDING_MEDIUM,
    paddingVertical: SIZES.PADDING_MEDIUM,
    borderRadius: SIZES.BORDER_RADIUS_MEDIUM,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: SIZES.MARGIN_SMALL,
  },
  searchPlaceholder: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: SIZES.FONT_MEDIUM,
  },
  section: {
    marginBottom: SIZES.MARGIN_LARGE,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SIZES.PADDING_LARGE,
    marginBottom: SIZES.MARGIN_MEDIUM,
  },
  sectionTitle: {
    fontSize: SIZES.FONT_LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    paddingHorizontal: SIZES.PADDING_LARGE,
    marginBottom: SIZES.MARGIN_MEDIUM,
  },
  seeAllText: {
    color: COLORS.PRIMARY,
    fontSize: SIZES.FONT_MEDIUM,
    fontWeight: '500',
  },
  categoriesScroll: {
    paddingLeft: SIZES.PADDING_LARGE,
  },
  categoryCard: {
    backgroundColor: COLORS.SURFACE,
    paddingHorizontal: SIZES.PADDING_MEDIUM,
    paddingVertical: SIZES.PADDING_SMALL,
    borderRadius: SIZES.BORDER_RADIUS_LARGE,
    marginRight: SIZES.MARGIN_SMALL,
  },
  categoryText: {
    fontSize: SIZES.FONT_SMALL,
    color: COLORS.TEXT_PRIMARY,
  },
  loadingContainer: {
    padding: SIZES.PADDING_LARGE,
    alignItems: 'center',
  },
  loadingText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: SIZES.FONT_MEDIUM,
  },
  restaurantCard: {
    width: 200,
    marginLeft: SIZES.PADDING_LARGE,
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: SIZES.BORDER_RADIUS_MEDIUM,
    shadowColor: COLORS.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  restaurantImage: {
    height: 120,
    backgroundColor: COLORS.SURFACE,
    borderTopLeftRadius: SIZES.BORDER_RADIUS_MEDIUM,
    borderTopRightRadius: SIZES.BORDER_RADIUS_MEDIUM,
    justifyContent: 'center',
    alignItems: 'center',
  },
  restaurantImagePlaceholder: {
    fontSize: 40,
  },
  restaurantInfo: {
    padding: SIZES.PADDING_MEDIUM,
  },
  restaurantName: {
    fontSize: SIZES.FONT_MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 4,
  },
  restaurantDetails: {
    fontSize: SIZES.FONT_SMALL,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 4,
  },
  restaurantCuisine: {
    fontSize: SIZES.FONT_SMALL,
    color: COLORS.TEXT_LIGHT,
  },
  popularRestaurantCard: {
    flexDirection: 'row',
    backgroundColor: COLORS.BACKGROUND,
    marginHorizontal: SIZES.PADDING_LARGE,
    marginBottom: SIZES.MARGIN_MEDIUM,
    borderRadius: SIZES.BORDER_RADIUS_MEDIUM,
    shadowColor: COLORS.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    padding: SIZES.PADDING_MEDIUM,
  },
  popularRestaurantImage: {
    width: 80,
    height: 80,
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MEDIUM,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SIZES.MARGIN_MEDIUM,
  },
  popularRestaurantInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  deliveryBadge: {
    backgroundColor: COLORS.SUCCESS,
    paddingHorizontal: SIZES.PADDING_SMALL,
    paddingVertical: 4,
    borderRadius: SIZES.BORDER_RADIUS_SMALL,
    alignSelf: 'flex-start',
  },
  deliveryBadgeText: {
    color: COLORS.BACKGROUND,
    fontSize: SIZES.FONT_SMALL,
    fontWeight: '500',
  },
});

export default HomeScreen;
