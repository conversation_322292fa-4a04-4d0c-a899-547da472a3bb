import React from 'react';
import { Alert } from 'react-native';
import axios from 'axios';
import CustomButton from './CustomButton';

const ApiTestButton: React.FC = () => {
  const testDirectApi = async () => {
    try {
      console.log('Testing direct API call...');
      
      const testData = {
        firstName: 'Test',
        lastName: 'User',
        email: `test${Date.now()}@example.com`,
        password: 'TestPassword123!',
        phone: '+1234567890',
      };

      console.log('Test data:', testData);
      console.log('API URL:', 'http://192.168.43.66:3001/api/v1/auth/register');

      // Test with direct axios call via proxy
      const response = await axios.post(
        'http://192.168.43.66:3001/api/v1/auth/register',
        testData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          timeout: 30000,
        }
      );

      console.log('Direct axios response:', response.data);
      Alert.alert('Success', 'Direct API call worked!');
    } catch (error: any) {
      console.error('Direct API test error:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error message:', error.message);
      
      Alert.alert(
        'API Test Failed',
        `Status: ${error.response?.status || 'Unknown'}\nMessage: ${error.message}`
      );
    }
  };

  const testFetch = async () => {
    try {
      console.log('Testing with fetch...');
      
      const testData = {
        firstName: 'Test',
        lastName: 'User',
        email: `test${Date.now()}@example.com`,
        password: 'TestPassword123!',
        phone: '+1234567890',
      };

      const response = await fetch(
        'http://192.168.43.66:3001/api/v1/auth/register',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(testData),
        }
      );

      console.log('Fetch response status:', response.status);
      const responseText = await response.text();
      console.log('Fetch response text:', responseText);

      if (response.ok) {
        Alert.alert('Success', 'Fetch API call worked!');
      } else {
        Alert.alert('Error', `Fetch failed with status: ${response.status}`);
      }
    } catch (error: any) {
      console.error('Fetch test error:', error);
      Alert.alert('Fetch Test Failed', error.message);
    }
  };

  return (
    <>
      <CustomButton
        title="Test Direct Axios"
        onPress={testDirectApi}
        variant="outline"
        style={{ marginBottom: 10 }}
      />
      <CustomButton
        title="Test Fetch"
        onPress={testFetch}
        variant="outline"
        style={{ marginBottom: 10 }}
      />
    </>
  );
};

export default ApiTestButton;
