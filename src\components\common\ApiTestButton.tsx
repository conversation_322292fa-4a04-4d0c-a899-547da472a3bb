import React from 'react';
import { Alert } from 'react-native';
import axios from 'axios';
import CustomButton from './CustomButton';

const ApiTestButton: React.FC = () => {
  const testBaseUrl = async () => {
    try {
      console.log('Testing base URL connectivity...');

      // Try a simple GET request to a known working endpoint
      const response = await fetch('https://jsonplaceholder.typicode.com/posts/1', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      console.log('External API test - Status:', response.status);
      const responseText = await response.text();
      console.log('External API test - Response:', responseText);

      if (response.ok) {
        Alert.alert('Success', 'External API is accessible! Network is working.');

        // Now test our backend
        try {
          const backendResponse = await fetch('https://backend-production-f106.up.railway.app/api/v1/auth/register', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: JSON.stringify({
              firstName: 'Test',
              lastName: 'User',
              email: `test${Date.now()}@example.com`,
              password: 'TestPassword123!',
              phone: '+1234567890',
            }),
          });

          console.log('Backend test - Status:', backendResponse.status);
          const backendText = await backendResponse.text();
          console.log('Backend test - Response:', backendText);

          Alert.alert('Backend Test', `Status: ${backendResponse.status}`);
        } catch (backendError: any) {
          console.error('Backend test error:', backendError);
          Alert.alert('Backend Test Failed', backendError.message);
        }
      } else {
        Alert.alert('Warning', `External API returned status: ${response.status}`);
      }
    } catch (error: any) {
      console.error('Network test error:', error);
      Alert.alert('Network Test Failed', `Network is not working: ${error.message}`);
    }
  };

  const testDirectApi = async () => {
    try {
      console.log('Testing direct API call...');
      
      const testData = {
        firstName: 'Test',
        lastName: 'User',
        email: `test${Date.now()}@example.com`,
        password: 'TestPassword123!',
        phone: '+1234567890',
      };

      console.log('Test data:', testData);
      console.log('API URL:', 'https://backend-production-f106.up.railway.app/api/v1/auth/register');

      // Test with direct axios call
      const response = await axios.post(
        'https://backend-production-f106.up.railway.app/api/v1/auth/register',
        testData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          timeout: 30000,
        }
      );

      console.log('Direct axios response:', response.data);
      Alert.alert('Success', 'Direct API call worked!');
    } catch (error: any) {
      console.error('Direct API test error:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error message:', error.message);
      
      Alert.alert(
        'API Test Failed',
        `Status: ${error.response?.status || 'Unknown'}\nMessage: ${error.message}`
      );
    }
  };

  const testFetch = async () => {
    try {
      console.log('Testing with fetch...');
      
      const testData = {
        firstName: 'Test',
        lastName: 'User',
        email: `test${Date.now()}@example.com`,
        password: 'TestPassword123!',
        phone: '+1234567890',
      };

      const response = await fetch(
        'https://backend-production-f106.up.railway.app/api/v1/auth/register',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(testData),
        }
      );

      console.log('Fetch response status:', response.status);
      const responseText = await response.text();
      console.log('Fetch response text:', responseText);

      if (response.ok) {
        Alert.alert('Success', 'Fetch API call worked!');
      } else {
        Alert.alert('Error', `Fetch failed with status: ${response.status}`);
      }
    } catch (error: any) {
      console.error('Fetch test error:', error);
      Alert.alert('Fetch Test Failed', error.message);
    }
  };

  return (
    <>
      <CustomButton
        title="Test Base URL"
        onPress={testBaseUrl}
        variant="outline"
        style={{ marginBottom: 10 }}
      />
      <CustomButton
        title="Test Direct Axios"
        onPress={testDirectApi}
        variant="outline"
        style={{ marginBottom: 10 }}
      />
      <CustomButton
        title="Test Fetch"
        onPress={testFetch}
        variant="outline"
        style={{ marginBottom: 10 }}
      />
    </>
  );
};

export default ApiTestButton;
